import React, {createContext, useContext, useEffect, useState} from "react";
import {
  useAccount,
  useBalance,
  useDisconnect,
  useChainId,
  useAccountEffect,
} from "wagmi";
import type {Address} from "viem";
import {formatAddress, formatBalance, getChainName} from "../utils/wallet";

type WalletContextType = {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  isReconnecting: boolean;
  address: Address | undefined;
  formattedAddress: string;

  // Balance and chain info
  balance: string;
  chainId: number;
  chainName: string;

  // Admin status (you can implement your own logic)
  isAdmin: boolean;

  // Connection status
  connectionStatus:
    | "disconnected"
    | "connecting"
    | "connected"
    | "reconnecting";

  // Actions
  disconnect: () => void;

  // Connection modal state
  isModalOpen: boolean;
  setIsModalOpen: (open: boolean) => void;
};

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export function WalletProvider({children}: {children: React.ReactNode}) {
  const {address, isConnected, isConnecting, isReconnecting} = useAccount();
  const {disconnect} = useDisconnect();
  const chainId = useChainId();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);

  // Get balance
  const {data: balanceData} = useBalance({
    address,
  });

  // Format address and balance
  const formattedAddress = formatAddress(address);
  const balance = formatBalance(balanceData?.value);
  const chainName = getChainName(chainId);

  // Determine connection status
  const getConnectionStatus = () => {
    if (isReconnecting) return "reconnecting";
    if (isConnecting) return "connecting";
    if (isConnected) return "connected";
    return "disconnected";
  };

  const connectionStatus = getConnectionStatus();

  // Admin check logic - you can customize this
  useEffect(() => {
    if (address) {
      // Example: Check if address is in admin list
      const adminAddresses = [
        // Add admin addresses here
        "******************************************",
      ];
      setIsAdmin(adminAddresses.includes(address.toLowerCase()));
    } else {
      setIsAdmin(false);
    }
  }, [address]);

  // Close modal when connected
  useEffect(() => {
    if (isConnected) {
      setIsModalOpen(false);
    }
  }, [isConnected]);

  // Handle account changes and cleanup
  useAccountEffect({
    onConnect(data) {
      console.log("Wallet connected:", data.address);
      setIsModalOpen(false);
    },
    onDisconnect() {
      console.log("Wallet disconnected");
      setIsAdmin(false);
      setIsModalOpen(false);
      // Clear any cached data or perform cleanup here
    },
  });

  // Enhanced disconnect function with cleanup
  const handleDisconnect = () => {
    try {
      disconnect();
      // Additional cleanup
      setIsAdmin(false);
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error disconnecting wallet:", error);
    }
  };

  return (
    <WalletContext.Provider
      value={{
        isConnected,
        isConnecting,
        isReconnecting,
        address,
        formattedAddress,
        balance,
        chainId,
        chainName,
        isAdmin,
        connectionStatus,
        disconnect: handleDisconnect,
        isModalOpen,
        setIsModalOpen,
      }}
    >
      {children}
    </WalletContext.Provider>
  );
}

export const useWallet = () => {
  const context = useContext(WalletContext);
  if (context === undefined) {
    throw new Error("useWallet must be used within a WalletProvider");
  }
  return context;
};
