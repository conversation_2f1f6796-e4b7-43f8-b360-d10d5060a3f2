import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDownIcon, CheckIcon, AlertTriangleIcon } from 'lucide-react';
import { useAccount, useChainId, useSwitchChain } from 'wagmi';
import { getChainName } from '../../utils/wallet';

const supportedChains = [
  { id: 1, name: 'Ethereum', color: 'bg-blue-500' },
  { id: 137, name: 'Polygon', color: 'bg-purple-500' },
  { id: 10, name: 'Optimism', color: 'bg-red-500' },
  { id: 42161, name: 'Arbitrum', color: 'bg-blue-600' },
  { id: 8453, name: 'Base', color: 'bg-blue-400' },
  { id: ********, name: '<PERSON>olia', color: 'bg-yellow-500' },
];

export function NetworkSwitcher() {
  const [isOpen, setIsOpen] = useState(false);
  const { isConnected } = useAccount();
  const chainId = useChainId();
  const { switchChain, isPending, error } = useSwitchChain();

  if (!isConnected) return null;

  const currentChain = supportedChains.find(chain => chain.id === chainId);
  const isUnsupportedChain = !currentChain;

  const handleSwitchChain = (targetChainId: number) => {
    switchChain({ chainId: targetChainId });
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
          isUnsupportedChain
            ? 'border-red-500 bg-red-500/10 text-red-400'
            : 'border-gray-600 bg-gray-700 text-gray-300 hover:bg-gray-600'
        }`}
        disabled={isPending}
      >
        {isUnsupportedChain ? (
          <>
            <AlertTriangleIcon size={16} />
            <span className="text-sm">Unsupported Network</span>
          </>
        ) : (
          <>
            <div className={`w-3 h-3 rounded-full ${currentChain.color}`} />
            <span className="text-sm">{currentChain.name}</span>
          </>
        )}
        <ChevronDownIcon 
          size={16} 
          className={`transition-transform ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      <AnimatePresence>
        {isOpen && (
          <>
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full mt-2 right-0 w-48 bg-gray-800 border border-gray-600 rounded-lg shadow-xl z-20"
            >
              <div className="p-2">
                <div className="text-xs text-gray-400 px-2 py-1 mb-1">
                  Switch Network
                </div>
                {supportedChains.map((chain) => (
                  <button
                    key={chain.id}
                    onClick={() => handleSwitchChain(chain.id)}
                    disabled={isPending}
                    className={`w-full flex items-center justify-between px-2 py-2 rounded-md text-sm transition-colors ${
                      chain.id === chainId
                        ? 'bg-indigo-600 text-white'
                        : 'text-gray-300 hover:bg-gray-700'
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${chain.color}`} />
                      <span>{chain.name}</span>
                    </div>
                    {chain.id === chainId && <CheckIcon size={16} />}
                  </button>
                ))}
              </div>
              
              {error && (
                <div className="border-t border-gray-600 p-2">
                  <div className="text-xs text-red-400 px-2">
                    Failed to switch network
                  </div>
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}
