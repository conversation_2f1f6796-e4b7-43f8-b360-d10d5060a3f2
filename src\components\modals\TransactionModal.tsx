import React, {useState, useEffect} from "react";
import {motion, AnimatePresence} from "framer-motion";
import {
  XIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  LoaderIcon,
  ExternalLinkIcon,
} from "lucide-react";
import {
  useSendTransaction,
  useWaitForTransactionReceipt,
  useAccount,
  useBalance,
} from "wagmi";
import {parseEther} from "viem";
import {Button} from "../ui/Button";
import {formatBalance, getExplorerUrl} from "../../utils/wallet";

type TransactionModalProps = {
  isOpen: boolean;
  onClose: () => void;
  property: {
    id: string;
    title: string;
    price: number;
    tokenPrice: number;
  };
};

export function TransactionModal({
  isOpen,
  onClose,
  property,
}: TransactionModalProps) {
  const [tokenAmount, setTokenAmount] = useState(1);
  const {address, chainId} = useAccount();

  // Get user's ETH balance
  const {data: balance} = useBalance({address});

  // Transaction hooks
  const {data: hash, error, isPending, sendTransaction} = useSendTransaction();

  const {
    isLoading: isConfirming,
    isSuccess: isConfirmed,
    error: receiptError,
  } = useWaitForTransactionReceipt({
    hash,
  });

  // Calculate transaction details
  const totalAmount = property.tokenPrice * tokenAmount;
  const estimatedGas = 0.0012; // ETH - this would be calculated dynamically in a real app
  const totalCost = totalAmount + estimatedGas;

  // Check if user has sufficient balance
  const hasInsufficientBalance =
    balance && parseEther(totalCost.toString()) > balance.value;

  const handleTransaction = () => {
    if (!address) return;

    // In a real app, this would be a contract interaction
    // For demo purposes, we'll send a small amount to a demo address
    sendTransaction({
      to: "******************************************", // Demo address
      value: parseEther("0.001"), // Small demo amount
    });
  };
  // Determine current status
  const getStatus = () => {
    if (isPending) return "processing";
    if (isConfirming) return "processing";
    if (isConfirmed) return "success";
    if (error || receiptError) return "error";
    return "initial";
  };

  const status = getStatus();

  const handleClose = () => {
    if (isPending || isConfirming) return;
    setTokenAmount(1);
    onClose();
  };
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{
              opacity: 0,
            }}
            animate={{
              opacity: 1,
            }}
            exit={{
              opacity: 0,
            }}
            onClick={handleClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
          />
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{
                opacity: 0,
                scale: 0.95,
                y: 20,
              }}
              animate={{
                opacity: 1,
                scale: 1,
                y: 0,
              }}
              exit={{
                opacity: 0,
                scale: 0.95,
                y: 20,
              }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-md bg-gray-800/90 backdrop-blur rounded-xl border border-gray-700 shadow-xl p-6"
            >
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-semibold text-white">
                  {status === "initial" && "Purchase Property Tokens"}
                  {status === "processing" && "Processing Transaction"}
                  {status === "success" && "Transaction Complete"}
                  {status === "error" && "Transaction Failed"}
                </h3>
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-white"
                  disabled={status === "processing"}
                >
                  <XIcon size={24} />
                </button>
              </div>
              {status === "initial" && (
                <>
                  <div className="mb-6">
                    <div className="text-gray-300 mb-4">
                      <p className="mb-1">Property: {property.title}</p>
                      <p className="mb-1">
                        Token Price: {property.tokenPrice} ETH
                      </p>
                      {balance && (
                        <p className="mb-1 text-sm text-gray-400">
                          Your Balance: {formatBalance(balance.value)} ETH
                        </p>
                      )}
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Number of Tokens to Purchase
                      </label>
                      <div className="flex items-center">
                        <button
                          onClick={() =>
                            setTokenAmount(Math.max(1, tokenAmount - 1))
                          }
                          className="bg-gray-700 text-white px-3 py-2 rounded-l-md hover:bg-gray-600"
                        >
                          -
                        </button>
                        <input
                          type="number"
                          value={tokenAmount}
                          onChange={(e) =>
                            setTokenAmount(parseInt(e.target.value) || 1)
                          }
                          min="1"
                          className="bg-gray-700 text-white text-center py-2 w-20 focus:outline-none"
                        />
                        <button
                          onClick={() => setTokenAmount(tokenAmount + 1)}
                          className="bg-gray-700 text-white px-3 py-2 rounded-r-md hover:bg-gray-600"
                        >
                          +
                        </button>
                      </div>
                    </div>
                    <div className="bg-gray-700 p-4 rounded-lg mb-6">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Subtotal</span>
                        <span className="text-white">
                          {(property.tokenPrice * tokenAmount).toFixed(4)} ETH
                        </span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Gas Fee (est.)</span>
                        <span className="text-white">0.0012 ETH</span>
                      </div>
                      <div className="border-t border-gray-600 my-2 pt-2 flex justify-between font-semibold">
                        <span className="text-gray-300">Total</span>
                        <span
                          className={
                            hasInsufficientBalance
                              ? "text-red-400"
                              : "text-indigo-400"
                          }
                        >
                          {totalCost.toFixed(4)} ETH
                        </span>
                      </div>
                      {hasInsufficientBalance && (
                        <div className="text-red-400 text-sm mt-2">
                          Insufficient balance
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    onClick={handleTransaction}
                    disabled={hasInsufficientBalance || !address}
                    fullWidth
                  >
                    {hasInsufficientBalance
                      ? "Insufficient Balance"
                      : "Confirm Purchase"}
                  </Button>
                </>
              )}
              {status === "processing" && (
                <div className="text-center py-8">
                  <motion.div
                    animate={{
                      rotate: 360,
                    }}
                    transition={{
                      repeat: Infinity,
                      duration: 1,
                      ease: "linear",
                    }}
                    className="mx-auto mb-4"
                  >
                    <LoaderIcon
                      size={48}
                      className="text-indigo-400"
                    />
                  </motion.div>
                  <p className="text-gray-300 mb-2">
                    {isPending
                      ? "Waiting for confirmation..."
                      : "Processing transaction..."}
                  </p>
                  <p className="text-sm text-gray-400">
                    {isPending
                      ? "Please confirm the transaction in your wallet"
                      : "Transaction is being processed on the blockchain"}
                  </p>
                  {hash && (
                    <div className="mt-4">
                      <a
                        href={getExplorerUrl(hash, chainId || 1)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-indigo-400 hover:text-indigo-300 text-sm"
                      >
                        View on Explorer
                        <ExternalLinkIcon
                          size={14}
                          className="ml-1"
                        />
                      </a>
                    </div>
                  )}
                </div>
              )}
              {status === "success" && (
                <div className="text-center py-6">
                  <motion.div
                    initial={{
                      scale: 0,
                    }}
                    animate={{
                      scale: 1,
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 200,
                      damping: 10,
                    }}
                    className="mx-auto mb-4 text-green-400"
                  >
                    <CheckCircleIcon size={48} />
                  </motion.div>
                  <h4 className="text-xl font-medium text-white mb-2">
                    Purchase Successful!
                  </h4>
                  <p className="text-gray-300 mb-4">
                    You have successfully purchased {tokenAmount} tokens of{" "}
                    {property.title}
                  </p>
                  {hash && (
                    <div className="mb-6">
                      <a
                        href={getExplorerUrl(hash, chainId || 1)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-indigo-400 hover:text-indigo-300 text-sm"
                      >
                        View Transaction
                        <ExternalLinkIcon
                          size={14}
                          className="ml-1"
                        />
                      </a>
                    </div>
                  )}
                  <Button
                    onClick={handleClose}
                    fullWidth
                  >
                    View My Portfolio
                  </Button>
                </div>
              )}
              {status === "error" && (
                <div className="text-center py-6">
                  <motion.div
                    initial={{
                      scale: 0,
                    }}
                    animate={{
                      scale: 1,
                    }}
                    transition={{
                      type: "spring",
                      stiffness: 200,
                      damping: 10,
                    }}
                    className="mx-auto mb-4 text-red-400"
                  >
                    <AlertCircleIcon size={48} />
                  </motion.div>
                  <h4 className="text-xl font-medium text-white mb-2">
                    Transaction Failed
                  </h4>
                  <p className="text-gray-300 mb-2">
                    There was an error processing your transaction.
                  </p>
                  {(error || receiptError) && (
                    <p className="text-red-400 text-sm mb-6 max-w-md mx-auto break-words">
                      {error?.message || receiptError?.message}
                    </p>
                  )}
                  <div className="flex space-x-3">
                    <Button
                      onClick={handleClose}
                      variant="secondary"
                      fullWidth
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleTransaction}
                      fullWidth
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  );
}
