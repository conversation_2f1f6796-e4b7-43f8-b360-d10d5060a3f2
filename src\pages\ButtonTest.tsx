import React, { useState } from 'react';
import { Button } from '../components/ui/Button';
import { WalletIcon, HomeIcon } from 'lucide-react';

export function ButtonTest() {
  const [isLoading, setIsLoading] = useState(false);

  const handleConnectWallet = () => {
    setIsLoading(true);
    // Simulate wallet connection
    setTimeout(() => {
      setIsLoading(false);
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Button Loading State Test</h1>
        
        <div className="space-y-8">
          {/* Connect Wallet Button with Loading */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Connect Wallet Button</h2>
            <div className="flex gap-4 items-center">
              <Button 
                onClick={handleConnectWallet}
                loading={isLoading}
                icon={<WalletIcon size={16} />}
              >
                Connect Wallet
              </Button>
              <span className="text-gray-400">
                {isLoading ? 'Connecting...' : 'Click to test loading state'}
              </span>
            </div>
          </div>

          {/* Different Button Variants with Loading */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Different Variants</h2>
            <div className="flex gap-4 flex-wrap">
              <Button loading={true} variant="primary">
                Primary Loading
              </Button>
              <Button loading={true} variant="secondary">
                Secondary Loading
              </Button>
              <Button loading={true} variant="outline">
                Outline Loading
              </Button>
              <Button loading={true} variant="danger">
                Danger Loading
              </Button>
            </div>
          </div>

          {/* Different Sizes with Loading */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Different Sizes</h2>
            <div className="flex gap-4 items-center">
              <Button loading={true} size="sm">
                Small
              </Button>
              <Button loading={true} size="md">
                Medium
              </Button>
              <Button loading={true} size="lg">
                Large
              </Button>
            </div>
          </div>

          {/* Normal vs Loading States */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Normal vs Loading Comparison</h2>
            <div className="flex gap-4">
              <Button icon={<HomeIcon size={16} />}>
                Normal Button
              </Button>
              <Button loading={true} icon={<HomeIcon size={16} />}>
                Loading Button
              </Button>
            </div>
          </div>

          {/* Full Width Loading */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Full Width Loading</h2>
            <Button loading={true} fullWidth>
              Full Width Loading Button
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
