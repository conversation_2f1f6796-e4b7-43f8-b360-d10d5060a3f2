{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@rainbow-me/rainbowkit": "^2.2.8", "@tanstack/react-query": "^5.81.2", "autoprefixer": "^10.4.21", "framer-motion": "^11.5.4", "lucide-react": "^0.441.0", "postcss": "^8.5.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "tailwindcss": "^3.4.0", "viem": "^2.31.4", "vite-plugin-optichunk": "^3.0.5", "wagmi": "^2.15.6"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.3", "eslint": "^9.29.0", "vite": "^6.0.1"}}