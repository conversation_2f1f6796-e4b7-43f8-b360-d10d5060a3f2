import { formatEther, parseEther } from 'viem';
import { type Address } from 'wagmi';

/**
 * Formats an Ethereum address for display
 * @param address - The full Ethereum address
 * @param startLength - Number of characters to show at the start (default: 6)
 * @param endLength - Number of characters to show at the end (default: 4)
 * @returns Formatted address string
 */
export function formatAddress(
  address: Address | string | undefined,
  startLength: number = 6,
  endLength: number = 4
): string {
  if (!address) return '';

  if (address.length <= startLength + endLength) {
    return address;
  }

  return `${address.substring(0, startLength)}...${address.substring(
    address.length - endLength
  )}`;
}

/**
 * Formats a balance value for display
 * @param balance - Balance in wei (bigint)
 * @param decimals - Number of decimal places to show (default: 4)
 * @returns Formatted balance string
 */
export function formatBalance(balance: bigint | undefined, decimals: number = 4): string {
  if (!balance) return '0';

  const formatted = formatEther(balance);
  const num = parseFloat(formatted);

  if (num === 0) return '0';
  if (num < 0.0001) return '< 0.0001';

  return num.toFixed(decimals);
}

/**
 * Converts ETH amount to wei
 * @param amount - Amount in ETH
 * @returns Amount in wei (bigint)
 */
export function ethToWei(amount: string | number): bigint {
  return parseEther(amount.toString());
}

/**
 * Converts wei to ETH
 * @param wei - Amount in wei (bigint)
 * @returns Amount in ETH (string)
 */
export function weiToEth(wei: bigint): string {
  return formatEther(wei);
}

/**
 * Checks if an address is valid
 * @param address - Address to validate
 * @returns True if valid Ethereum address
 */
export function isValidAddress(address: string): boolean {
  return /^0x[a-fA-F0-9]{40}$/.test(address);
}

/**
 * Gets the explorer URL for a transaction
 * @param txHash - Transaction hash
 * @param chainId - Chain ID
 * @returns Explorer URL
 */
export function getExplorerUrl(txHash: string, chainId: number): string {
  const explorers: Record<number, string> = {
    1: 'https://etherscan.io',
    137: 'https://polygonscan.com',
    10: 'https://optimistic.etherscan.io',
    42161: 'https://arbiscan.io',
    8453: 'https://basescan.org',
    11155111: 'https://sepolia.etherscan.io',
  };

  const baseUrl = explorers[chainId] || 'https://etherscan.io';
  return `${baseUrl}/tx/${txHash}`;
}

/**
 * Gets the explorer URL for an address
 * @param address - Ethereum address
 * @param chainId - Chain ID
 * @returns Explorer URL
 */
export function getAddressExplorerUrl(address: string, chainId: number): string {
  const explorers: Record<number, string> = {
    1: 'https://etherscan.io',
    137: 'https://polygonscan.com',
    10: 'https://optimistic.etherscan.io',
    42161: 'https://arbiscan.io',
    8453: 'https://basescan.org',
    11155111: 'https://sepolia.etherscan.io',
  };

  const baseUrl = explorers[chainId] || 'https://etherscan.io';
  return `${baseUrl}/address/${address}`;
}

/**
 * Gets chain name from chain ID
 * @param chainId - Chain ID
 * @returns Chain name
 */
export function getChainName(chainId: number): string {
  const chains: Record<number, string> = {
    1: 'Ethereum',
    137: 'Polygon',
    10: 'Optimism',
    42161: 'Arbitrum',
    8453: 'Base',
    11155111: 'Sepolia',
  };

  return chains[chainId] || 'Unknown';
}

/**
 * Transaction status types
 */
export type TransactionStatus = 'idle' | 'pending' | 'success' | 'error';

/**
 * Formats transaction status for display
 * @param status - Transaction status
 * @returns Formatted status string
 */
export function formatTransactionStatus(status: TransactionStatus): string {
  const statusMap: Record<TransactionStatus, string> = {
    idle: 'Ready',
    pending: 'Processing...',
    success: 'Completed',
    error: 'Failed',
  };

  return statusMap[status];
}

/**
 * Gets status color for UI
 * @param status - Transaction status
 * @returns Tailwind color class
 */
export function getStatusColor(status: TransactionStatus): string {
  const colorMap: Record<TransactionStatus, string> = {
    idle: 'text-gray-400',
    pending: 'text-yellow-400',
    success: 'text-green-400',
    error: 'text-red-400',
  };

  return colorMap[status];
}

/**
 * Formats a token amount with proper decimals
 * @param amount - Token amount (bigint)
 * @param decimals - Token decimals (default: 18)
 * @param displayDecimals - Number of decimals to display (default: 4)
 * @returns Formatted token amount
 */
export function formatTokenAmount(
  amount: bigint | undefined,
  decimals: number = 18,
  displayDecimals: number = 4
): string {
  if (!amount) return '0';

  const divisor = BigInt(10 ** decimals);
  const quotient = amount / divisor;
  const remainder = amount % divisor;

  const wholeNumber = quotient.toString();
  const fractionalPart = remainder.toString().padStart(decimals, '0');

  const formatted = `${wholeNumber}.${fractionalPart}`;
  const num = parseFloat(formatted);

  if (num === 0) return '0';
  if (num < 1 / (10 ** displayDecimals)) return `< ${(1 / (10 ** displayDecimals)).toFixed(displayDecimals)}`;

  return num.toFixed(displayDecimals);
}

/**
 * Calculates percentage change
 * @param current - Current value
 * @param previous - Previous value
 * @returns Percentage change
 */
export function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return 0;
  return ((current - previous) / previous) * 100;
}

/**
 * Formats percentage for display
 * @param percentage - Percentage value
 * @param decimals - Number of decimal places (default: 2)
 * @returns Formatted percentage string
 */
export function formatPercentage(percentage: number, decimals: number = 2): string {
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(decimals)}%`;
}

/**
 * Gets color class for percentage change
 * @param percentage - Percentage value
 * @returns Tailwind color class
 */
export function getPercentageColor(percentage: number): string {
  if (percentage > 0) return 'text-green-400';
  if (percentage < 0) return 'text-red-400';
  return 'text-gray-400';
}

/**
 * Truncates text with ellipsis
 * @param text - Text to truncate
 * @param maxLength - Maximum length
 * @returns Truncated text
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return `${text.substring(0, maxLength)}...`;
}

/**
 * Formats a large number with appropriate suffix (K, M, B)
 * @param num - Number to format
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted number string
 */
export function formatLargeNumber(num: number, decimals: number = 1): string {
  if (num >= 1e9) {
    return `${(num / 1e9).toFixed(decimals)}B`;
  }
  if (num >= 1e6) {
    return `${(num / 1e6).toFixed(decimals)}M`;
  }
  if (num >= 1e3) {
    return `${(num / 1e3).toFixed(decimals)}K`;
  }
  return num.toString();
}

/**
 * Debounce function for search inputs
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
