import React, {useState, useEffect} from "react";
import {useLocation, Link} from "react-router-dom";
import {motion} from "framer-motion";
import {
  HomeIcon,
  SearchIcon,
  LayoutDashboardIcon,
  MenuIcon,
  XIcon,
} from "lucide-react";

import {useWallet} from "../../context/WalletContext";
import {ConnectButton} from "@rainbow-me/rainbowkit";
import {Button} from "../ui/Button";
import {Logo} from "../ui/Logo";
import {NetworkSwitcher} from "../ui/NetworkSwitcher";

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isWalletConnecting, setIsWalletConnecting] = useState(false);

  const {
    isConnected,
    isConnecting,
    isReconnecting,
    formattedAddress,
    balance,
    isAdmin,
    disconnect,
  } = useWallet();
  const {pathname} = useLocation();

  // Reset loading state when wallet connects
  useEffect(() => {
    if (isConnected) {
      setIsWalletConnecting(false);
    }
  }, [isConnected]);

  const navLinks = [
    {name: "Home", path: "/", icon: <HomeIcon size={16} />},
    {name: "Browse", path: "/browse", icon: <SearchIcon size={16} />},
  ];

  if (isConnected) {
    navLinks.push({
      name: "Dashboard",
      path: "/user",
      icon: <LayoutDashboardIcon size={16} />,
    });
  }

  if (isAdmin) {
    navLinks.push({
      name: "Admin",
      path: "/admin",
      icon: <LayoutDashboardIcon size={16} />,
    });
  }

  return (
    <>
      <nav className="bg-gray-800 border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link
                to="/"
                className="flex-shrink-0"
              >
                <Logo />
              </Link>
              <div className="hidden md:block ml-10">
                <div className="flex items-center space-x-4">
                  {navLinks.map((link) => (
                    <Link
                      key={link.path}
                      to={link.path}
                      className={`${
                        pathname === link.path
                          ? "bg-gray-700 text-white"
                          : "text-gray-300 hover:bg-gray-700 hover:text-white"
                      } px-3 py-2 rounded-md text-sm font-medium flex items-center`}
                    >
                      <span className="mr-1">{link.icon}</span>
                      {link.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <div className="hidden md:block">
              <div className="ml-4 flex items-center md:ml-6">
                {isConnected ? (
                  <div className="flex items-center space-x-3">
                    <NetworkSwitcher />
                    <div className="text-right">
                      <div className="text-sm text-gray-300">
                        {formattedAddress}
                      </div>
                      <div className="text-xs text-gray-400">{balance} ETH</div>
                    </div>
                    <Button
                      onClick={disconnect}
                      variant="secondary"
                    >
                      Disconnect
                    </Button>
                  </div>
                ) : (
                  <ConnectButton.Custom>
                    {({openConnectModal}) => (
                      <Button
                        onClick={() => {
                          setIsWalletConnecting(true);
                          setTimeout(() => {
                            setIsWalletConnecting(false);
                            openConnectModal();
                          }, 500);
                        }}
                        loading={
                          isWalletConnecting || isConnecting || isReconnecting
                        }
                      >
                        Connect Wallet
                      </Button>
                    )}
                  </ConnectButton.Custom>
                )}
              </div>
            </div>

            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none"
              >
                {isMenuOpen ? <XIcon size={24} /> : <MenuIcon size={24} />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <motion.div
            className="md:hidden"
            initial={{height: 0, opacity: 0}}
            animate={{height: "auto", opacity: 1}}
            exit={{height: 0, opacity: 0}}
            transition={{duration: 0.2}}
          >
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
              {navLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`${
                    pathname === link.path
                      ? "bg-gray-700 text-white"
                      : "text-gray-300 hover:bg-gray-700 hover:text-white"
                  } px-3 py-2 rounded-md text-base font-medium flex items-center`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="mr-2">{link.icon}</span>
                  {link.name}
                </Link>
              ))}

              {isConnected ? (
                <div className="px-3 py-2">
                  <div className="mb-2">
                    <div className="text-gray-300 text-sm">
                      {formattedAddress}
                    </div>
                    <div className="text-gray-400 text-xs">{balance} ETH</div>
                  </div>
                  <Button
                    onClick={disconnect}
                    variant="secondary"
                    fullWidth
                  >
                    Disconnect
                  </Button>
                </div>
              ) : (
                <div className="px-3 py-2">
                  <ConnectButton.Custom>
                    {({openConnectModal}) => (
                      <Button
                        onClick={() => {
                          setIsWalletConnecting(true);
                          setIsMenuOpen(false);
                          setTimeout(() => {
                            setIsWalletConnecting(false);
                            openConnectModal();
                          }, 500);
                        }}
                        fullWidth
                        loading={
                          isWalletConnecting || isConnecting || isReconnecting
                        }
                      >
                        Connect Wallet
                      </Button>
                    )}
                  </ConnectButton.Custom>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </nav>
    </>
  );
}
